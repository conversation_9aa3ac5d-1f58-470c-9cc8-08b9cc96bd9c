package org;

import com.google.gson.Gson;

import java.io.FileReader;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;

public class main {
    public static void main(String[] args) {
        Gson gson = new Gson();
        try {
            //读取配置文件
            FileReader reader=new FileReader("src/main/resources/config.json");
            Config config = gson.fromJson(reader, Config.class);
            System.out.println(config);

            //创建server
            ServerSocket socket=new ServerSocket(config.getPort());
            while (true) {
                Socket accept = socket.accept();
                PrintWriter out = new PrintWriter(accept.getOutputStream(), true);
                out.println("成功链接服务器"+config.getName());
                System.out.println("成功链接服务器");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
